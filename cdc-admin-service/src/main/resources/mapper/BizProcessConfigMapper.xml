<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.BizProcessConfigMapper">

    <select id="queryByOrgIdAndDiseaseCode" resultType="com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO">
        select
        a.id,
        a.org_id as orgId,
        a.config_id as configId,
        a.sub_system_code as subSystemCode,
        b.type as type,
        b.disease_code as diseaseCode,
        b.disease_name as diseaseName,
        b.time_limit as timeLimit,
        to_char(b.update_time, 'yyyy-MM-dd HH:mm:ss') as updateTime
        from app.tb_cdcmr_biz_process_config a
        join
        app.tb_cdcmr_check_authority_config b on a.config_id = b.id and disease_code = #{diseaseCode}
        where a.org_id = #{orgId}
        and a.sub_system_code = #{subSystemCode}
        and b.delete_flag = '0'
        limit 1
    </select>
    <select id="queryList" resultType="com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO">
        select
        a.id,
        a.org_id as orgId,
        a.config_id as configId,
        a.sub_system_code as subSystemCode,
        b.type as type,
        b.disease_code as diseaseCode,
        b.disease_name as diseaseName,
        b.time_limit as timeLimit,
        to_char(b.update_time, 'yyyy-MM-dd HH:mm:ss') as updateTime,
        b.check_level as checkLevel,
        b.check_person as checkPerson
        from app.tb_cdcmr_biz_process_config a
        join
        app.tb_cdcmr_check_authority_config b on a.config_id = b.id
        where a.org_id = #{orgId}
        and a.sub_system_code = #{subSystemCode}
        and b.delete_flag = '0'
        <if test="type != null and type != ''">
            and b.type = #{type}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and b.disease_name like concat('%', #{diseaseName}, '%')
        </if>
        <if test="timeLimit != null">
            and b.time_limit = #{timeLimit}
        </if>
        <if test="status != null">
            and b.status = #{status}
        </if>
        <if test="startDate != null and endDate != null">
            and b.update_time between #{startDate} and #{endDate}
        </if>
        order by b.update_time desc, b.id
    </select>
</mapper>