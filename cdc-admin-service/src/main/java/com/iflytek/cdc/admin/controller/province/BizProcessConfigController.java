package com.iflytek.cdc.admin.controller.province;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityEditDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityQueryDTO;
import com.iflytek.cdc.admin.service.BizProcessConfigService;
import com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/pt/{version}/bizProcess/config")
public class BizProcessConfigController {


    private BizProcessConfigService bizProcessConfigService;

    @Autowired
    public void setBizProcessConfigService(BizProcessConfigService bizProcessConfigService) {
        this.bizProcessConfigService = bizProcessConfigService;
    }

    /**
     * 新增配置
     */
    @PostMapping("/create")
    public Boolean create(@RequestBody BizProcessCheckAuthorityDTO dto,
                          @RequestParam String loginUserId){
        return bizProcessConfigService.create(dto,loginUserId);
    }

    /**
     * 删除配置
     */
    @PostMapping("/delete")
    public Boolean delete(@RequestParam String configId,
                          @RequestParam String loginUserId){
        return bizProcessConfigService.delete(configId,loginUserId);
    }

    /**
     * 配置列表
     */
    @PostMapping("/list")
    public PageInfo<BizProcessCheckAuthorityVO> queryList(@RequestBody BizProcessCheckAuthorityQueryDTO dto){
        return bizProcessConfigService.queryList(dto);
    }

    /**
     * 编辑权限
     */
    @PostMapping("/editPermission")
    public boolean editPermission(@RequestBody BizProcessCheckAuthorityEditDTO dto,
                        @RequestParam String loginUserId){
        return bizProcessConfigService.editPermission(dto,loginUserId);
    }

    /**
     * 查询权限
     */
    @GetMapping("/queryPermission")
    public BizProcessCheckAuthorityVO queryPermission(@RequestParam String configId ){
        return bizProcessConfigService.queryPermission(configId);
    }

    /**
     * 查询配置责任人
     */
    @GetMapping("/queryDeptUser")
    public List<UapOrgUser> queryDeptUser(@RequestParam String orgId,
                                          @RequestParam String loginUserId){
        return bizProcessConfigService.queryDeptUser(orgId,loginUserId);
    }
}
