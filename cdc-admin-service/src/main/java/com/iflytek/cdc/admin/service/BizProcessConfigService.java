package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityEditDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityQueryDTO;
import com.iflytek.cdc.admin.entity.BizProcessConfig;
import com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO;

import java.util.List;

public interface BizProcessConfigService extends IService<BizProcessConfig> {
    Boolean create(BizProcessCheckAuthorityDTO dto, String loginUserId);

    Boolean delete(String configId, String loginUserId);

    PageInfo<BizProcessCheckAuthorityVO> queryList(BizProcessCheckAuthorityQueryDTO dto);

    boolean editPermission(BizProcessCheckAuthorityEditDTO dto, String loginUserId);

    BizProcessCheckAuthorityVO queryPermission(String configId);

    List<UapOrgUser> queryDeptUser(String orgId,String loginUserId);
}
