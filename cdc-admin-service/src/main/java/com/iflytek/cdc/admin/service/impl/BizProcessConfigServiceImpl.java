package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapLazyOrgInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityEditDTO;
import com.iflytek.cdc.admin.dto.BizProcessCheckAuthorityQueryDTO;
import com.iflytek.cdc.admin.entity.BizProcessConfig;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.admin.mapper.BizProcessConfigMapper;
import com.iflytek.cdc.admin.service.BizProcessConfigService;
import com.iflytek.cdc.admin.service.province.ProcessCheckConfigService;
import com.iflytek.cdc.admin.vo.BizProcessCheckAuthorityVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapOrganization;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgDto;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.enums.BizSystemEnum.CDC_DISEASE_REPORT;
import static com.iflytek.cdc.admin.enums.BizSystemEnum.CDC_SYNDROME_MONITOR;
import static com.iflytek.cdc.admin.enums.OrgNodeTypeEnum.DEPARTMENT;

@Service
public class BizProcessConfigServiceImpl extends ServiceImpl<BizProcessConfigMapper, BizProcessConfig>
        implements BizProcessConfigService {

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }


    private ProcessCheckConfigService processCheckConfigService;

    @Autowired
    public void setProcessCheckConfigService(ProcessCheckConfigService processCheckConfigService) {
        this.processCheckConfigService = processCheckConfigService;
    }

    private UapOrgApi uapOrgApi;

    @Autowired
    public void setUapOrgApi(UapOrgApi uapOrgApi) {
        this.uapOrgApi = uapOrgApi;
    }


    private BizProcessConfigMapper bizProcessConfigMapper;

    @Autowired
    public void setBizProcessConfigMapper(BizProcessConfigMapper bizProcessConfigMapper) {
        this.bizProcessConfigMapper = bizProcessConfigMapper;
    }


    @Transactional
    @Override
    public Boolean create(BizProcessCheckAuthorityDTO dto, String loginUserId) {
        String orgId = dto.getOrgId();
        if (StrUtil.isBlank(orgId)){
            throw new MedicalBusinessException("缺少参数");
        }
        String diseaseCode = dto.getDiseaseCode();
        String diseaseName = dto.getDiseaseName();
        if (StrUtil.isBlank(diseaseCode) || StrUtil.isBlank(diseaseName)){
            throw new MedicalBusinessException("缺少参数");
        }
        String subSystemCode = dto.getSubSystemCode();
        if (StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }
        // 校验配置是否已经创建
        boolean checkFlag = checkConfigExist(dto);
        if (checkFlag){
            throw new MedicalBusinessException("当前疾病配置已存在");
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);

        // 先增加配置
        TbCdcmrCheckAuthorityConfig tbCdcmrCheckAuthorityConfig = new TbCdcmrCheckAuthorityConfig();
        tbCdcmrCheckAuthorityConfig.setId(String.valueOf(batchUidService.getUid(TbCdcmrCheckAuthorityConfig.TB_NAME)));
        tbCdcmrCheckAuthorityConfig.setDiseaseCode(diseaseCode);
        tbCdcmrCheckAuthorityConfig.setDiseaseName(diseaseName);
        tbCdcmrCheckAuthorityConfig.setStatus(1);
        tbCdcmrCheckAuthorityConfig.setDeleteFlag("0");

        tbCdcmrCheckAuthorityConfig.setCreator(user.getName());
        tbCdcmrCheckAuthorityConfig.setCreatorId(loginUserId);
        tbCdcmrCheckAuthorityConfig.setCreateTime(new Date());
        tbCdcmrCheckAuthorityConfig.setUpdater(user.getName());
        tbCdcmrCheckAuthorityConfig.setUpdaterId(loginUserId);
        tbCdcmrCheckAuthorityConfig.setUpdateTime(new Date());

        buildType(subSystemCode, tbCdcmrCheckAuthorityConfig);

        // 新增
        boolean saveFlag = processCheckConfigService.save(tbCdcmrCheckAuthorityConfig);
        if (!saveFlag){
            throw new MedicalBusinessException("新增配置失败");
        }
        // 新增配置关联
        String configId = tbCdcmrCheckAuthorityConfig.getId();
        BizProcessConfig bizProcessConfig = new BizProcessConfig();
        bizProcessConfig.setId(String.valueOf(batchUidService.getUid(BizProcessConfig.TB_NAME)));
        bizProcessConfig.setOrgId(orgId);
        bizProcessConfig.setConfigId(configId);
        bizProcessConfig.setSubSystemCode(subSystemCode);

        bizProcessConfig.setCreator(user.getName());
        bizProcessConfig.setCreatorId(loginUserId);
        bizProcessConfig.setCreateTime(new Date());
        bizProcessConfig.setUpdater(user.getName());
        bizProcessConfig.setUpdaterId(loginUserId);
        bizProcessConfig.setUpdateTime(new Date());

        // 获取机构的省市区
        buildOrgRegionInfo(orgId, bizProcessConfig);
        boolean saveFlag2 = this.save(bizProcessConfig);
        if (!saveFlag2){
            throw new MedicalBusinessException("新增配置失败");
        }
        return true;
    }

    private boolean checkConfigExist(BizProcessCheckAuthorityDTO dto) {

        BizProcessCheckAuthorityVO bizProcessCheckAuthorityVO = bizProcessConfigMapper.queryByOrgIdAndDiseaseCode(dto);
        return bizProcessCheckAuthorityVO != null;
    }

    private void buildOrgRegionInfo(String orgId, BizProcessConfig bizProcessConfig) {
        UapOrgDto uapOrgDto = uapOrgApi.getExt(orgId);
        if (uapOrgDto != null){
            TUapOrganization uapOrganization = uapOrgDto.getUapOrganization();
            if (uapOrganization != null){
                String province = uapOrganization.getProvince();
                if (StrUtil.isNotBlank(province)){
                    bizProcessConfig.setProvinceName(province);
                }
                String provinceCode = uapOrganization.getProvinceCode();
                if (StrUtil.isNotBlank(provinceCode)){
                    bizProcessConfig.setProvinceCode(provinceCode);
                }

                String city = uapOrganization.getCity();
                if (StrUtil.isNotBlank(city)){
                    bizProcessConfig.setCityName(city);
                }

                String cityCode = uapOrganization.getCityCode();
                if (StrUtil.isNotBlank(cityCode)) {
                    bizProcessConfig.setCityCode(cityCode);
                }

                String district = uapOrganization.getDistrict();
                if (StrUtil.isNotBlank(district)) {
                    bizProcessConfig.setDistrictName(district);
                }
                String districtCode = uapOrganization.getDistrictCode();
                if (StrUtil.isNotBlank(districtCode)) {
                    bizProcessConfig.setDistrictCode(districtCode);
                }
            }
        }
    }

    private void buildType(String subSystemCode, TbCdcmrCheckAuthorityConfig tbCdcmrCheckAuthorityConfig) {
        if (StrUtil.equals(subSystemCode,CDC_DISEASE_REPORT.getCode())){
            tbCdcmrCheckAuthorityConfig.setType("infected");
        } else if (StrUtil.equals(subSystemCode,CDC_SYNDROME_MONITOR.getCode())){
            tbCdcmrCheckAuthorityConfig.setType("syndrome");
        }
    }


    @Override
    public Boolean delete(String configId, String loginUserId) {
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        if (StrUtil.isBlank(configId)){
            throw new MedicalBusinessException("缺少参数");
        }

        TbCdcmrCheckAuthorityConfig checkAuthorityConfig = processCheckConfigService.getById(configId);
        if (checkAuthorityConfig == null){
            throw new MedicalBusinessException("未查询到该配置");
        }
        checkAuthorityConfig.setDeleteFlag("1");
        checkAuthorityConfig.setUpdater(user.getName());
        checkAuthorityConfig.setUpdaterId(loginUserId);
        checkAuthorityConfig.setUpdateTime(new Date());

        return processCheckConfigService.updateById(checkAuthorityConfig);
    }

    @Override
    public PageInfo<BizProcessCheckAuthorityVO> queryList(BizProcessCheckAuthorityQueryDTO dto) {

        String orgId = dto.getOrgId();

        String subSystemCode = dto.getSubSystemCode();
        if (StrUtil.isBlank(orgId) || StrUtil.isBlank(subSystemCode)){
            throw new MedicalBusinessException("缺少参数");
        }
        List<BizProcessCheckAuthorityVO> list = bizProcessConfigMapper.queryList(dto);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(list);
    }

    @Override
    public boolean editPermission(BizProcessCheckAuthorityEditDTO dto, String loginUserId) {
        String configId = dto.getConfigId();
        if (StrUtil.isBlank(configId)){
            throw new MedicalBusinessException("缺少参数");
        }
        TbCdcmrCheckAuthorityConfig checkAuthorityConfig = processCheckConfigService.getById(configId);
        if (checkAuthorityConfig == null){
            throw new MedicalBusinessException("未查询到该配置");
        }
        String checkPerson = dto.getCheckPerson();
        String checkLevel = dto.getCheckLevel();
        String timeLimit = dto.getTimeLimit();
        if (StrUtil.isNotBlank(checkPerson)){
            checkAuthorityConfig.setCheckPerson(checkPerson);
        }
        if (StrUtil.isNotBlank(checkLevel)){
            checkAuthorityConfig.setCheckLevel(checkLevel);
        }

        if (StrUtil.isNotBlank(timeLimit)){
            try {
                int timeLimit_INT = Integer.parseInt(timeLimit);
                checkAuthorityConfig.setTimeLimit(timeLimit_INT);
            } catch (NumberFormatException e) {
                throw new MedicalBusinessException("审核时效为整数");
            }
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        checkAuthorityConfig.setUpdater(user.getName());
        checkAuthorityConfig.setUpdaterId(loginUserId);
        checkAuthorityConfig.setUpdateTime(new Date());
        return processCheckConfigService.updateById(checkAuthorityConfig);
    }

    @Override
    public BizProcessCheckAuthorityVO queryPermission(String configId) {
        if (StrUtil.isBlank(configId)){
            throw new MedicalBusinessException("缺少参数");
        }

        return null;
    }

    @Override
    public List<UapOrgUser> queryDeptUser(String orgId,String loginUserId) {
        List<UapLazyOrgInfo> uapLazyOrgInfoList = uapServiceApi.lazyOrgOrDept(orgId, loginUserId);
        List<UapLazyOrgInfo> departmentList = uapLazyOrgInfoList.stream()
                                                         .filter(item -> StrUtil.equals(item.getNodeType(), DEPARTMENT.getCode()))
                                                         .collect(Collectors.toList());
        if (departmentList.isEmpty()){
            return Collections.emptyList();
        }
        List<String> departmentIdList = departmentList.stream().map(UapLazyOrgInfo::getId).collect(Collectors.toList());

        return Collections.emptyList();
    }
}
